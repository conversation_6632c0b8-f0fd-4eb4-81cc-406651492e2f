from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Optional
import uvicorn
import openai
import os
import json
import platform
import subprocess
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

app = FastAPI()

# Initialize Groq client (using OpenAI SDK with Groq base URL)
openai_client = openai.OpenAI(
    api_key=os.getenv("GROQ_API_KEY"),
    base_url="https://api.groq.com/openai/v1"
)

# Available Groq models for system administration and package management (Updated 2024)
AVAILABLE_MODELS = {
    "llama-3.3-70b-versatile": {
        "name": "Llama 3.3 70B Versatile",
        "description": "Latest Llama model, best for complex system administration tasks",
        "context": 131072,
        "recommended": True
    },
    "llama-3.1-8b-instant": {
        "name": "Llama 3.1 8B Instant",
        "description": "Fastest model for simple tasks",
        "context": 131072,
        "recommended": False
    },
    "gemma2-9b-it": {
        "name": "Gemma2 9B Instruct",
        "description": "Google's efficient model, good balance of speed and capability",
        "context": 8192,
        "recommended": False
    },
    "deepseek-r1-distill-llama-70b": {
        "name": "DeepSeek R1 Distilled Llama 70B",
        "description": "Efficient, high-quality model for complex tasks",
        "context": 131072,
        "recommended": False
    },
    "moonshotai/kimi-k2-instruct": {
        "name": "Moonshot AI Kimi K2 Instruct",
        "description": "High-quality, fast model for general tasks",
        "context": 8192,
        "recommended": False
    }
}

# Default model (always use Kimi K2 as requested)
DEFAULT_MODEL = "moonshotai/kimi-k2-instruct"

# Allow requests from frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # for dev only
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class SystemInfo(BaseModel):
    os: str
    arch: str
    distro: str
    package_manager: Optional[str] = None
    python_version: Optional[str] = None

class InstallRequest(BaseModel):
    query: str
    system_info: SystemInfo
    model: Optional[str] = DEFAULT_MODEL

class CheckRequirementsRequest(BaseModel):
    commands: List[str]
    system_info: SystemInfo

class CommandResult(BaseModel):
    command: str
    success: bool
    output: str
    error: Optional[str] = None

def detect_package_manager(system_info: SystemInfo) -> str:
    """Detect the appropriate package manager based on system info."""
    os_name = system_info.os.lower()
    distro = system_info.distro.lower()

    if "ubuntu" in distro or "debian" in distro:
        return "apt"
    elif "centos" in distro or "rhel" in distro or "fedora" in distro:
        return "yum" if "centos" in distro else "dnf"
    elif "arch" in distro:
        return "pacman"
    elif os_name == "darwin":  # macOS
        return "brew"
    elif os_name == "windows":
        return "choco"  # or winget
    else:
        return "apt"  # default fallback

def get_openai_requirements_prompt(query: str, system_info: SystemInfo) -> str:
    """Generate a prompt for OpenRouter/GPT-3.5 to determine package requirements."""
    package_manager = detect_package_manager(system_info)

    return f"""
You are a system administrator helping to install software packages.

User request: "{query}"
System Information:
- OS: {system_info.os}
- Architecture: {system_info.arch}
- Distribution: {system_info.distro}
- Package Manager: {package_manager}

Please provide a JSON response with the following structure:
{{
    "check_commands": ["command1", "command2", "command3"],
    "package_name": "actual_package_name_with_version_if_specified",
    "requirements": ["req1", "req2"],
    "explanation": "Brief explanation of what will be installed"
}}

IMPORTANT REQUIREMENTS:

1. BASIC SYSTEM CHECKS: Always include fundamental system checks first:
   - Check if package manager is available (e.g., "which apt", "which yum")
   - Check if system can install packages ("apt list --installed | head -1" or equivalent)
   - Check internet connectivity ("ping -c 1 8.8.8.8" or "curl -s --head http://google.com")

2. VERSION HANDLING: If user specifies a version (e.g., "python 3.9", "nodejs 16", "install docker 20.10"):
   - Extract the exact version from the request
   - Include version-specific checks (e.g., "python3.9 --version", "node --version | grep 16")
   - Provide exact version in package_name

3. COMPREHENSIVE CHECKS: Include checks for:
   - Package manager availability
   - Internet connectivity
   - Existing installations
   - Dependencies
   - System compatibility

Example check_commands for basic system:
["which {package_manager}", "ping -c 1 8.8.8.8", "which python3", "python3 --version"]

Use the appropriate package manager commands for {package_manager}.
If version is specified, be very precise about version checking and installation.

CRITICAL: You must respond with ONLY valid JSON in this exact format:
{{
    "check_commands": ["command1", "command2"],
    "package_name": "exact_package_name",
    "requirements": ["req1", "req2"],
    "explanation": "Brief explanation"
}}

Do not include any text before or after the JSON. Do not use markdown code blocks.
"""

def get_openai_install_prompt(query: str, system_info: SystemInfo, check_results: List[CommandResult]) -> str:
    """Generate a prompt for OpenRouter/GPT-3.5 to create installation commands."""
    package_manager = detect_package_manager(system_info)

    check_status = []
    for result in check_results:
        status = "EXISTS" if result.success else "MISSING"
        output_preview = result.output[:100] if result.output else "No output"
        check_status.append(f"- {result.command}: {status} ({output_preview})")

    return f"""
You are a system administrator creating installation commands.

User request: "{query}"
System Information:
- OS: {system_info.os}
- Architecture: {system_info.arch}
- Distribution: {system_info.distro}
- Package Manager: {package_manager}

Requirement Check Results:
{chr(10).join(check_status)}

IMPORTANT INSTRUCTIONS:

1. VERSION PRECISION: If user specified a version, install EXACTLY that version:
   - For "python 3.9": use "apt install python3.9" not generic python3
   - For "nodejs 16": use version-specific commands
   - For "docker 20.10": specify exact version

2. COMPREHENSIVE INSTALLATION: Based on check results, provide commands to:
   - Update package manager if needed
   - Install missing dependencies first
   - Install the exact package/version requested
   - Verify installation success

3. COMMAND STRUCTURE: Provide complete, ready-to-execute commands:
   - Include all necessary flags and options
   - Use full package names with versions when specified
   - Add verification commands at the end

Respond with JSON in this format:
{{
    "commands": ["command1", "command2", "command3"],
    "explanation": "What these commands will do",
    "estimated_time": "estimated installation time"
}}

Use the appropriate package manager commands for {package_manager}.
Include sudo where necessary for system-level installations.
Be very precise about versions if specified in the user request.

CRITICAL: You must respond with ONLY valid JSON in this exact format:
{{
    "commands": ["command1", "command2"],
    "explanation": "What these commands will do",
    "estimated_time": "estimated installation time"
}}

Do not include any text before or after the JSON. Do not use markdown code blocks.
"""

def validate_command_safety(command: str) -> bool:
    """Basic validation to prevent obviously dangerous commands."""
    dangerous_patterns = [
        'rm -rf /',
        'dd if=',
        'mkfs',
        'fdisk',
        'format',
        '> /dev/',
        'chmod 777 /',
        'chown -R',
        'sudo su',
        'passwd',
        'userdel',
        'deluser',
        'shutdown',
        'reboot',
        'halt',
        'init 0',
        'init 6',
        'systemctl poweroff',
        'systemctl reboot'
    ]

    command_lower = command.lower()
    for pattern in dangerous_patterns:
        if pattern in command_lower:
            return False

    return True

def sanitize_openai_response(response_text: str) -> dict:
    """Safely parse AI response and validate the structure with fallback handling."""
    print(f"🔧 [DEBUG] Raw AI response to parse: {response_text[:200]}...")

    try:
        # Remove any markdown code blocks if present
        cleaned_text = response_text.strip()

        if '```json' in cleaned_text:
            start = cleaned_text.find('```json') + 7
            end = cleaned_text.find('```', start)
            if end != -1:
                cleaned_text = cleaned_text[start:end].strip()
        elif '```' in cleaned_text:
            start = cleaned_text.find('```') + 3
            end = cleaned_text.find('```', start)
            if end != -1:
                cleaned_text = cleaned_text[start:end].strip()

        # Try to find JSON object in the text
        json_start = cleaned_text.find('{')
        json_end = cleaned_text.rfind('}') + 1

        if json_start != -1 and json_end > json_start:
            json_text = cleaned_text[json_start:json_end]
            print(f"🔧 [DEBUG] Extracted JSON: {json_text[:100]}...")
        else:
            json_text = cleaned_text

        data = json.loads(json_text)
        print(f"✅ [DEBUG] Successfully parsed JSON")

        # Validate required fields and sanitize commands
        if 'commands' in data:
            safe_commands = []
            for cmd in data['commands']:
                if validate_command_safety(cmd):
                    safe_commands.append(cmd)
                else:
                    print(f"⚠️ [DEBUG] Skipping dangerous command: {cmd}")
            data['commands'] = safe_commands

        return data

    except json.JSONDecodeError as e:
        print(f"❌ [DEBUG] JSON parsing failed: {e}")
        print(f"❌ [DEBUG] Problematic text: {response_text}")

        # Fallback: Try to extract information manually
        return create_fallback_response(response_text)
    except Exception as e:
        print(f"❌ [DEBUG] General parsing error: {e}")
        return create_fallback_response(response_text)

def create_fallback_response(response_text: str) -> dict:
    """Create a fallback response when JSON parsing fails."""
    print(f"🔄 [DEBUG] Creating fallback response from: {response_text[:100]}...")

    # Try to extract commands from the text
    commands = []
    lines = response_text.split('\n')

    for line in lines:
        line = line.strip()
        # Look for command-like patterns
        if any(cmd in line.lower() for cmd in ['sudo', 'pacman', 'apt', 'yum', 'dnf', 'install', 'update']):
            # Clean up the line
            if line.startswith('-'):
                line = line[1:].strip()
            if line.startswith('*'):
                line = line[1:].strip()
            if line.startswith('`') and line.endswith('`'):
                line = line[1:-1]

            if line and validate_command_safety(line):
                commands.append(line)

    # If no commands found, provide basic fallback
    if not commands:
        commands = ["echo 'AI response parsing failed, manual intervention required'"]

    fallback_response = {
        "commands": commands,
        "explanation": "Fallback response due to AI parsing issues",
        "estimated_time": "Unknown",
        "check_commands": ["echo 'Fallback mode'"],
        "package_name": "unknown",
        "requirements": []
    }

    print(f"🔄 [DEBUG] Fallback response created with {len(commands)} commands")
    return fallback_response

@app.post("/get-requirements")
async def get_requirements(request: InstallRequest):
    """Phase 1: Get requirements and check commands from OpenRouter/GPT-3.5."""
    try:
        print(f"\n🔍 [DEBUG] GET-REQUIREMENTS REQUEST RECEIVED")
        print(f"📝 Query: '{request.query}'")
        print(f"💻 System: {request.system_info.os} {request.system_info.arch}")
        print(f"📦 Package Manager: {request.system_info.package_manager}")

        # Validate input
        if not request.query.strip():
            print("❌ [DEBUG] Empty query provided")
            raise HTTPException(status_code=400, detail="Query cannot be empty")

        if len(request.query) > 500:
            print(f"❌ [DEBUG] Query too long: {len(request.query)} characters")
            raise HTTPException(status_code=400, detail="Query too long (max 500 characters)")

        # Check if API key is configured
        groq_key = os.getenv("GROQ_API_KEY")
        gemini_key = os.getenv("GEMINI_API_KEY")
        openrouter_key = os.getenv("OPENROUTER_API_KEY")

        if not groq_key and not gemini_key and not openrouter_key:
            print("❌ [DEBUG] No API key configured")
            raise HTTPException(status_code=500, detail="No API key configured")

        api_provider = "Groq" if groq_key else ("Gemini" if gemini_key else "OpenRouter")
        print(f"🔑 [DEBUG] Using {api_provider} API")

        # Validate model selection
        selected_model = request.model or DEFAULT_MODEL
        if selected_model not in AVAILABLE_MODELS:
            print(f"❌ [DEBUG] Invalid model: {selected_model}")
            raise HTTPException(status_code=400, detail=f"Invalid model: {selected_model}")

        print(f"🤖 [DEBUG] Using model: {selected_model} ({AVAILABLE_MODELS[selected_model]['name']})")
        # Get requirements from AI
        print(f"🤖 [DEBUG] Generating AI prompt...")
        prompt = get_openai_requirements_prompt(request.query, request.system_info)
        print(f"📄 [DEBUG] Prompt length: {len(prompt)} characters")

        print(f"🚀 [DEBUG] Sending request to AI API...")
        try:
            response = openai_client.chat.completions.create(
                model=selected_model,
                messages=[
                    {"role": "system", "content": "You are a helpful system administrator."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1
            )
            print(f"✅ [DEBUG] AI response received successfully")
        except Exception as ai_error:
            print(f"❌ [DEBUG] AI API request failed: {str(ai_error)}")
            raise HTTPException(status_code=500, detail=f"AI API request failed: {str(ai_error)}")

        # Parse and validate the JSON response
        print(f"🔧 [DEBUG] Parsing AI response...")
        raw_response = response.choices[0].message.content
        print(f"📝 [DEBUG] Raw AI response: {raw_response[:200]}...")

        requirements_data = sanitize_openai_response(raw_response)
        print(f"✅ [DEBUG] Response parsed successfully")
        print(f"📦 [DEBUG] Package name: {requirements_data.get('package_name', 'Unknown')}")
        print(f"🔍 [DEBUG] Check commands: {len(requirements_data.get('check_commands', []))}")

        result = {
            "check_commands": requirements_data.get("check_commands", []),
            "package_name": requirements_data.get("package_name", ""),
            "requirements": requirements_data.get("requirements", []),
            "explanation": requirements_data.get("explanation", ""),
            "phase": "requirements_check"
        }

        print(f"🎉 [DEBUG] GET-REQUIREMENTS completed successfully")
        print(f"📤 [DEBUG] Returning {len(result['check_commands'])} check commands")
        return result

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ [DEBUG] Unexpected error in get-requirements: {str(e)}")
        print(f"Error getting requirements: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting requirements: {str(e)}")

@app.post("/get-install-commands")
async def get_install_commands(request: CheckRequirementsRequest):
    """Phase 2: Get installation commands based on check results."""
    try:
        # Convert commands to CommandResult objects (assuming they come from UI)
        check_results = []
        for cmd in request.commands:
            # This will be populated by the UI with actual results
            check_results.append(CommandResult(
                command=cmd,
                success=False,  # Will be updated by UI
                output="",
                error=None
            ))

        # For now, return a simple response - this will be enhanced when UI sends actual results
        package_manager = detect_package_manager(request.system_info)

        return {
            "commands": [f"echo 'Ready to install using {package_manager}'"],
            "explanation": "Installation commands will be generated based on check results",
            "phase": "installation"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating install commands: {str(e)}")

class CheckResultsRequest(BaseModel):
    query: str
    system_info: SystemInfo
    check_results: List[CommandResult]
    model: Optional[str] = DEFAULT_MODEL

@app.post("/generate-install-commands")
async def generate_install_commands(request: CheckResultsRequest):
    """Generate final installation commands based on actual check results."""
    try:
        print(f"\n🔧 [DEBUG] GENERATE-INSTALL-COMMANDS REQUEST RECEIVED")
        print(f"📝 Query: '{request.query}'")
        print(f"🔍 Check results: {len(request.check_results)} items")

        for i, result in enumerate(request.check_results):
            status = "✅ PASS" if result.success else "❌ FAIL"
            print(f"  {i+1}. {result.command}: {status}")

        # Validate input
        if not request.query.strip():
            print("❌ [DEBUG] Empty query provided")
            raise HTTPException(status_code=400, detail="Query cannot be empty")

        if not request.check_results:
            print("❌ [DEBUG] No check results provided")
            raise HTTPException(status_code=400, detail="Check results cannot be empty")

        # Check if API key is configured
        groq_key = os.getenv("GROQ_API_KEY")
        gemini_key = os.getenv("GEMINI_API_KEY")
        openrouter_key = os.getenv("OPENROUTER_API_KEY")

        if not groq_key and not gemini_key and not openrouter_key:
            print("❌ [DEBUG] No API key configured")
            raise HTTPException(status_code=500, detail="No API key configured")

        api_provider = "Groq" if groq_key else ("Gemini" if gemini_key else "OpenRouter")
        print(f"🔑 [DEBUG] Using {api_provider} API for install commands")

        # Validate model selection
        selected_model = request.model or DEFAULT_MODEL
        if selected_model not in AVAILABLE_MODELS:
            print(f"❌ [DEBUG] Invalid model: {selected_model}")
            raise HTTPException(status_code=400, detail=f"Invalid model: {selected_model}")

        print(f"🤖 [DEBUG] Using model: {selected_model} ({AVAILABLE_MODELS[selected_model]['name']})")
        # Get installation commands from AI
        print(f"🤖 [DEBUG] Generating install commands prompt...")
        prompt = get_openai_install_prompt(request.query, request.system_info, request.check_results)
        print(f"📄 [DEBUG] Install prompt length: {len(prompt)} characters")

        print(f"🚀 [DEBUG] Sending install request to AI API...")
        try:
            response = openai_client.chat.completions.create(
                model=selected_model,
                messages=[
                    {"role": "system", "content": "You are a helpful system administrator."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1
            )
            print(f"✅ [DEBUG] AI install response received successfully")
        except Exception as ai_error:
            print(f"❌ [DEBUG] AI API install request failed: {str(ai_error)}")
            raise HTTPException(status_code=500, detail=f"AI API request failed: {str(ai_error)}")

        # Parse and validate the JSON response
        print(f"🔧 [DEBUG] Parsing AI install response...")
        raw_response = response.choices[0].message.content
        print(f"📝 [DEBUG] Raw AI install response: {raw_response[:200]}...")

        install_data = sanitize_openai_response(raw_response)
        print(f"✅ [DEBUG] Install response parsed successfully")
        print(f"⚙️ [DEBUG] Install commands: {len(install_data.get('commands', []))}")

        result = {
            "commands": install_data.get("commands", []),
            "explanation": install_data.get("explanation", ""),
            "estimated_time": install_data.get("estimated_time", "Unknown"),
            "phase": "installation"
        }

        print(f"🎉 [DEBUG] GENERATE-INSTALL-COMMANDS completed successfully")
        print(f"📤 [DEBUG] Returning {len(result['commands'])} install commands")
        return result

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ [DEBUG] Unexpected error in generate-install-commands: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error generating install commands: {str(e)}")

@app.get("/models")
async def list_models():
    """List available models."""
    return {
        "available_models": AVAILABLE_MODELS,
        "default_model": DEFAULT_MODEL,
        "recommended_model": next(
            (model_id for model_id, info in AVAILABLE_MODELS.items() if info.get("recommended")),
            DEFAULT_MODEL
        )
    }

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    groq_configured = bool(os.getenv("GROQ_API_KEY"))
    gemini_configured = bool(os.getenv("GEMINI_API_KEY"))
    openrouter_configured = bool(os.getenv("GEMINI_API_KEY"))

    return {
        "status": "healthy",
        "groq_configured": groq_configured,
        "gemini_configured": gemini_configured,
        "openrouter_configured": openrouter_configured,
        "current_model": DEFAULT_MODEL,
        "api_provider": "Groq" if groq_configured else ("Gemini" if gemini_configured else "OpenRouter"),
        "version": "1.0.0"
    }

if __name__ == "__main__":
    port = int(os.getenv("SERVER_PORT", 8000))
    host = os.getenv("SERVER_HOST", "0.0.0.0")
    uvicorn.run("server:app", host=host, port=port, reload=True)
