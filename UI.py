import tkinter as tk
import requests
import subprocess
import threading
import platform
import json
import sys
import shutil
import os
import tempfile
import time

SERVER_BASE_URL = "http://localhost:8000"

# Global variables for progress tracking
progress_var = None
progress_label = None

def update_progress(percentage, message=""):
    """Update the progress bar and message."""
    global progress_var, progress_label
    if progress_var and progress_label:
        # Create progress bar visual
        bar_width = 50
        filled = int(bar_width * percentage / 100)
        bar = "=" * filled + "-" * (bar_width - filled)

        progress_text = f"[{bar}] {percentage}%"
        if message:
            progress_text += f" - {message}"

        progress_var.set(progress_text)
        progress_label.update()

def select_smart_model(query, user_selected_model):
    """Always use Kimi K2 as requested."""
    # Always use Kimi K2 for everything
    return "moonshotai/kimi-k2-instruct"

def log_debug(message, level="INFO"):
    """Add debug message to status text with timestamp."""
    timestamp = time.strftime("%H:%M:%S")
    level_icons = {
        "INFO": "ℹ️",
        "SUCCESS": "✅",
        "ERROR": "❌",
        "DEBUG": "🔍",
        "API": "🤖",
        "NETWORK": "🌐"
    }
    icon = level_icons.get(level, "📝")

    debug_message = f"[{timestamp}] {icon} {message}\n"
    status_text.insert(tk.END, debug_message)
    status_text.see(tk.END)
    root.update()

def detect_package_manager():
    """Detect the available package manager on the system."""
    managers = {
        'apt': 'apt-get',
        'yum': 'yum',
        'dnf': 'dnf',
        'pacman': 'pacman',
        'brew': 'brew',
        'choco': 'choco'
    }

    for name, command in managers.items():
        if shutil.which(command):
            return name
    return None

def get_python_version():
    """Get the current Python version."""
    try:
        return f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    except:
        return None

def get_system_info():
    """Get comprehensive system information."""
    return {
        "os": platform.system(),
        "arch": platform.machine(),
        "distro": platform.platform(),
        "package_manager": detect_package_manager(),
        "python_version": get_python_version()
    }

def generate_verification_commands(package_name):
    """Generate verification commands based on package name."""
    package_lower = package_name.lower()

    # Common verification patterns
    verification_commands = []

    if 'python' in package_lower:
        if '3.9' in package_lower:
            verification_commands = ["python3.9 --version", "python3.9 -c 'print(\"Python 3.9 is working!\")'"]
        elif '3.8' in package_lower:
            verification_commands = ["python3.8 --version", "python3.8 -c 'print(\"Python 3.8 is working!\")'"]
        else:
            verification_commands = ["python3 --version", "python3 -c 'print(\"Python is working!\")'"]

    elif 'node' in package_lower or 'npm' in package_lower:
        verification_commands = ["node --version", "npm --version", "node -e 'console.log(\"Node.js is working!\")'"]

    elif 'docker' in package_lower:
        verification_commands = ["docker --version", "docker info", "sudo systemctl status docker"]

    elif 'git' in package_lower:
        verification_commands = ["git --version", "git config --list"]

    elif 'java' in package_lower:
        verification_commands = ["java -version", "javac -version"]

    elif 'mysql' in package_lower:
        verification_commands = ["mysql --version", "sudo systemctl status mysql"]

    elif 'nginx' in package_lower:
        verification_commands = ["nginx -v", "sudo systemctl status nginx"]

    elif 'apache' in package_lower:
        verification_commands = ["apache2 -v", "sudo systemctl status apache2"]

    elif 'php' in package_lower:
        verification_commands = ["php --version", "php -r 'echo \"PHP is working!\";'"]

    elif 'curl' in package_lower:
        verification_commands = ["curl --version", "curl -s http://httpbin.org/get > /dev/null && echo 'curl network test passed'"]

    elif 'wget' in package_lower:
        verification_commands = ["wget --version", "wget -q --spider http://httpbin.org/get && echo 'wget network test passed'"]

    else:
        # Generic verification - try to find the command
        base_name = package_lower.replace('.io', '').replace('-', '').replace('_', '')
        verification_commands = [
            f"which {base_name}",
            f"{base_name} --version 2>/dev/null || {base_name} -v 2>/dev/null || {base_name} version 2>/dev/null || echo '{base_name} command found but version check failed'"
        ]

    return verification_commands

def detect_terminal():
    """Detect available terminal emulator."""
    terminals = [
        'gnome-terminal',
        'konsole',
        'xfce4-terminal',
        'lxterminal',
        'mate-terminal',
        'terminator',
        'xterm',
        'urxvt',
        'alacritty',
        'kitty'
    ]

    for terminal in terminals:
        if shutil.which(terminal):
            return terminal
    return None

def create_script_file(check_commands, install_commands, package_name):
    """Create a temporary script file for terminal execution."""
    script_content = "#!/bin/bash\n"
    script_content += "# Disable shell integrations that might cause conflicts\n"
    script_content += "unset PYENV_SHELL\n"
    script_content += "export PATH=/usr/local/bin:/usr/bin:/bin:$PATH\n"
    script_content += "clear\n"  # Clear terminal immediately
    script_content += f"echo '=== AI Package Installer - {package_name} ==='\n"
    script_content += "echo 'Starting execution...'\n"
    script_content += "echo ''\n"

    if check_commands:
        script_content += "echo '=== CHECKING REQUIREMENTS ==='\n"
        for i, cmd in enumerate(check_commands, 1):
            script_content += f"echo 'Check {i}: {cmd}'\n"
            script_content += f"{cmd}\n"
            script_content += f"echo 'Exit code: $?'\n"
            script_content += "echo ''\n"
        script_content += "echo 'Requirements check completed!'\n"
        script_content += "echo ''\n"

    if install_commands:
        script_content += "echo '=== INSTALLING PACKAGES ==='\n"
        for i, cmd in enumerate(install_commands, 1):
            script_content += f"echo 'Install {i}: {cmd}'\n"
            # Add automatic yes responses for interactive prompts
            if any(keyword in cmd.lower() for keyword in ['apt install', 'apt-get install', 'yum install', 'dnf install']):
                if '-y' not in cmd and '--yes' not in cmd:
                    cmd = cmd.replace('install', 'install -y')
            script_content += f"echo 'Y' | {cmd}\n"
            script_content += f"INSTALL_EXIT_CODE=$?\n"
            script_content += f"echo 'Exit code: $INSTALL_EXIT_CODE'\n"
            script_content += f"if [ $INSTALL_EXIT_CODE -ne 0 ]; then\n"
            script_content += f"    echo '❌ Installation step {i} failed!'\n"
            script_content += f"    echo 'Please check the error above.'\n"
            script_content += f"    exit 1\n"
            script_content += f"fi\n"
            script_content += "echo ''\n"
        script_content += "echo 'Installation commands completed!'\n"
        script_content += "echo ''\n"

    # Add verification steps
    script_content += "echo '=== VERIFYING INSTALLATION ==='\n"
    script_content += f"echo 'Verifying {package_name} installation...'\n"

    # Add package-specific verification commands
    verification_commands = generate_verification_commands(package_name)
    for i, cmd in enumerate(verification_commands, 1):
        script_content += f"echo 'Verification {i}: {cmd}'\n"
        script_content += f"{cmd}\n"
        script_content += f"VERIFY_EXIT_CODE=$?\n"
        script_content += f"if [ $VERIFY_EXIT_CODE -eq 0 ]; then\n"
        script_content += f"    echo '✅ Verification {i} passed'\n"
        script_content += f"else\n"
        script_content += f"    echo '⚠️  Verification {i} failed (exit code: $VERIFY_EXIT_CODE)'\n"
        script_content += f"fi\n"
        script_content += "echo ''\n"

    script_content += "echo '=== FINAL STATUS ==='\n"
    script_content += f"echo 'Package: {package_name}'\n"
    script_content += "echo 'Installation: COMPLETED'\n"
    script_content += "echo 'Verification: See results above'\n"
    script_content += "echo ''\n"
    script_content += "echo '🎉 All operations completed!'\n"
    script_content += "echo 'Terminal will remain open for review.'\n"
    script_content += "exec bash\n"  # Keep terminal open

    # Create temporary script file
    script_fd, script_path = tempfile.mkstemp(suffix='.sh', prefix='ai_installer_')
    with os.fdopen(script_fd, 'w') as f:
        f.write(script_content)

    # Make script executable
    os.chmod(script_path, 0o755)
    return script_path

def analyze_prompt_and_respond(command, sudo_password):
    """Analyze command prompts and generate appropriate responses using AI."""
    try:
        # Use a lightweight model for prompt analysis
        prompt_analysis_payload = {
            "query": f"Analyze this command and predict what prompts it might show: '{command}'. Return a JSON with likely prompts and appropriate responses.",
            "system_info": get_system_info(),
            "model": "llama-3.1-8b-instant"  # Fast model for prompt analysis
        }

        response = requests.post(f"{SERVER_BASE_URL}/get-requirements", json=prompt_analysis_payload, timeout=10)
        if response.status_code == 200:
            # For now, use basic heuristics until we implement the full AI analysis
            pass
    except:
        pass

    # Basic heuristic responses for common prompts
    responses = {
        "proceed": "Y",
        "continue": "Y",
        "yes/no": "Y",
        "[Y/n]": "Y",
        "[y/N]": "Y",
        "password": sudo_password,
        "sudo": sudo_password,
        "confirm": "Y",
        "overwrite": "Y",
        "replace": "Y"
    }

    return responses

def create_agent_script_file(check_commands, install_commands, package_name, sudo_password):
    """Create a temporary script file for agent mode execution with smart prompt handling."""
    script_content = "#!/bin/bash\n"
    script_content += "# Disable shell integrations that might cause conflicts\n"
    script_content += "unset PYENV_SHELL\n"
    script_content += "export PATH=/usr/local/bin:/usr/bin:/bin:$PATH\n"
    script_content += "clear\n"
    script_content += f"echo '=== AI Package Installer - AGENT MODE - {package_name} ==='\n"
    script_content += "echo 'Running in automated mode with smart prompt handling...'\n"
    script_content += "echo ''\n"

    # Create expect script for smart prompt handling
    script_content += "# Function to handle interactive prompts intelligently\n"
    script_content += "smart_execute() {\n"
    script_content += "    local cmd=\"$1\"\n"
    script_content += "    echo \"Executing: $cmd\"\n"
    script_content += "    \n"
    script_content += "    # Use expect for smart prompt handling\n"
    script_content += "    expect -c \"\n"
    script_content += "        set timeout 300\n"
    script_content += "        spawn bash -c \\\"$cmd\\\"\n"
    script_content += "        expect {\n"
    script_content += "            -re {[Pp]assword.*:} {\n"
    script_content += f"                send \\\"{sudo_password}\\\\r\\\"\n"
    script_content += "                exp_continue\n"
    script_content += "            }\n"
    script_content += "            -re {\\\\[Y/n\\\\]} {\n"
    script_content += "                send \\\"Y\\\\r\\\"\n"
    script_content += "                exp_continue\n"
    script_content += "            }\n"
    script_content += "            -re {\\\\[y/N\\\\]} {\n"
    script_content += "                send \\\"Y\\\\r\\\"\n"
    script_content += "                exp_continue\n"
    script_content += "            }\n"
    script_content += "            -re {[Pp]roceed.*\\\\?} {\n"
    script_content += "                send \\\"Y\\\\r\\\"\n"
    script_content += "                exp_continue\n"
    script_content += "            }\n"
    script_content += "            -re {[Cc]ontinue.*\\\\?} {\n"
    script_content += "                send \\\"Y\\\\r\\\"\n"
    script_content += "                exp_continue\n"
    script_content += "            }\n"
    script_content += "            -re {[Oo]verwrite.*\\\\?} {\n"
    script_content += "                send \\\"Y\\\\r\\\"\n"
    script_content += "                exp_continue\n"
    script_content += "            }\n"
    script_content += "            eof\n"
    script_content += "        }\n"
    script_content += "        catch wait result\n"
    script_content += "        exit [lindex \\$result 3]\n"
    script_content += "    \"\n"
    script_content += "}\n\n"

    if check_commands:
        script_content += "echo '=== CHECKING REQUIREMENTS ==='\n"
        for i, cmd in enumerate(check_commands, 1):
            script_content += f"echo 'Check {i}: {cmd}'\n"
            # Use expect for automatic password entry if sudo is needed
            if 'sudo' in cmd:
                script_content += f"echo '{sudo_password}' | sudo -S {cmd.replace('sudo ', '')}\n"
            else:
                script_content += f"{cmd}\n"
            script_content += f"echo 'Exit code: $?'\n"
            script_content += "echo ''\n"
        script_content += "echo 'Requirements check completed!'\n"
        script_content += "echo ''\n"

    if install_commands:
        script_content += "echo '=== INSTALLING PACKAGES ==='\n"
        for i, cmd in enumerate(install_commands, 1):
            script_content += f"echo 'Install {i}: {cmd}'\n"
            # Use smart execution function
            script_content += f"smart_execute \"{cmd}\"\n"
            script_content += f"INSTALL_EXIT_CODE=$?\n"
            script_content += f"echo 'Exit code: $INSTALL_EXIT_CODE'\n"
            script_content += f"if [ $INSTALL_EXIT_CODE -ne 0 ]; then\n"
            script_content += f"    echo '❌ Installation step {i} failed!'\n"
            script_content += f"    echo 'Please check the error above.'\n"
            script_content += f"    exit 1\n"
            script_content += f"fi\n"
            script_content += "echo ''\n"
        script_content += "echo 'Installation commands completed!'\n"
        script_content += "echo ''\n"

    # Add verification steps for agent mode
    script_content += "echo '=== VERIFYING INSTALLATION ==='\n"
    script_content += f"echo 'Verifying {package_name} installation...'\n"

    verification_commands = generate_verification_commands(package_name)
    for i, cmd in enumerate(verification_commands, 1):
        script_content += f"echo 'Verification {i}: {cmd}'\n"
        script_content += f"{cmd}\n"
        script_content += f"VERIFY_EXIT_CODE=$?\n"
        script_content += f"if [ $VERIFY_EXIT_CODE -eq 0 ]; then\n"
        script_content += f"    echo '✅ Verification {i} passed'\n"
        script_content += f"else\n"
        script_content += f"    echo '⚠️  Verification {i} failed (exit code: $VERIFY_EXIT_CODE)'\n"
        script_content += f"fi\n"
        script_content += "echo ''\n"

    script_content += "echo '=== FINAL STATUS ==='\n"
    script_content += f"echo 'Package: {package_name}'\n"
    script_content += "echo 'Installation: COMPLETED'\n"
    script_content += "echo 'Verification: See results above'\n"
    script_content += "echo ''\n"
    script_content += "echo '🎉 All operations completed automatically!'\n"
    script_content += "echo 'Agent mode execution finished.'\n"
    script_content += "exec bash\n"  # Keep terminal open

    # Create temporary script file
    script_fd, script_path = tempfile.mkstemp(suffix='.sh', prefix='ai_installer_agent_')
    with os.fdopen(script_fd, 'w') as f:
        f.write(script_content)

    # Make script executable
    os.chmod(script_path, 0o755)
    return script_path

def open_terminal_with_commands(check_commands, install_commands, package_name, title="AI Installer"):
    """Open a new terminal window and execute commands."""
    terminal = detect_terminal()
    if not terminal:
        return False, "No terminal emulator found"

    try:
        script_path = create_script_file(check_commands, install_commands, package_name)

        # Terminal-specific command construction
        if terminal == 'gnome-terminal':
            cmd = [terminal, '--title', title, '--', 'bash', script_path]
        elif terminal == 'konsole':
            cmd = [terminal, '--title', title, '-e', 'bash', script_path]
        elif terminal in ['xfce4-terminal', 'mate-terminal']:
            cmd = [terminal, '--title', title, '-e', f'bash {script_path}']
        elif terminal == 'terminator':
            cmd = [terminal, '--title', title, '-e', f'bash {script_path}']
        elif terminal in ['xterm', 'urxvt']:
            cmd = [terminal, '-title', title, '-e', 'bash', script_path]
        elif terminal in ['alacritty', 'kitty']:
            cmd = [terminal, '--title', title, '-e', 'bash', script_path]
        else:
            cmd = [terminal, '-e', f'bash {script_path}']

        # Launch terminal with immediate execution
        subprocess.Popen(cmd, start_new_session=True,
                        stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        return True, f"Opened {terminal} for package installation"

    except Exception as e:
        return False, f"Failed to open terminal: {str(e)}"

def open_terminal_agent_mode(check_commands, install_commands, package_name, sudo_password, title="AI Installer Agent"):
    """Open a new terminal window in agent mode with automatic sudo execution."""
    terminal = detect_terminal()
    if not terminal:
        return False, "No terminal emulator found"

    try:
        script_path = create_agent_script_file(check_commands, install_commands, package_name, sudo_password)

        # Terminal-specific command construction
        if terminal == 'gnome-terminal':
            cmd = [terminal, '--title', title, '--', 'bash', script_path]
        elif terminal == 'konsole':
            cmd = [terminal, '--title', title, '-e', 'bash', script_path]
        elif terminal in ['xfce4-terminal', 'mate-terminal']:
            cmd = [terminal, '--title', title, '-e', f'bash {script_path}']
        elif terminal == 'terminator':
            cmd = [terminal, '--title', title, '-e', f'bash {script_path}']
        elif terminal in ['xterm', 'urxvt']:
            cmd = [terminal, '-title', title, '-e', 'bash', script_path]
        elif terminal in ['alacritty', 'kitty']:
            cmd = [terminal, '--title', title, '-e', 'bash', script_path]
        else:
            cmd = [terminal, '-e', f'bash {script_path}']

        # Launch terminal with immediate execution
        subprocess.Popen(cmd, start_new_session=True,
                        stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        return True, f"Opened {terminal} in agent mode"

    except Exception as e:
        return False, f"Failed to open terminal in agent mode: {str(e)}"

def run_command_in_background(command, use_sudo=False):
    """Run a command in background to check status without opening terminal."""
    try:
        full_command = f"sudo {command}" if use_sudo else command
        result = subprocess.run(full_command, shell=True, capture_output=True, text=True, timeout=30)

        return {
            "command": command,
            "success": result.returncode == 0,
            "output": result.stdout.strip(),
            "error": result.stderr.strip() if result.stderr else None,
            "return_code": result.returncode
        }
    except subprocess.TimeoutExpired:
        return {
            "command": command,
            "success": False,
            "output": "",
            "error": "Command timed out after 30 seconds",
            "return_code": -1
        }
    except Exception as e:
        return {
            "command": command,
            "success": False,
            "output": "",
            "error": str(e),
            "return_code": -1
        }

def install_package():
    user_input = entry.get().strip()
    if not user_input:
        log_debug("No package specified", "ERROR")
        return

    # Check if agent mode is enabled
    agent_mode = agent_mode_var.get()
    sudo_password = password_entry.get() if agent_mode else None
    selected_model = model_var.get()

    if agent_mode and not sudo_password:
        log_debug("Agent mode requires sudo password", "ERROR")
        return

    # Initialize progress
    update_progress(0, "Starting installation process...")

    mode_text = "AGENT MODE" if agent_mode else "MANUAL MODE"
    model_name = next((name for name, value in model_options if value == selected_model), selected_model)
    log_debug(f"Installing: {user_input} ({mode_text})", "INFO")
    log_debug(f"Using AI Model: {model_name}", "INFO")
    status_text.insert(tk.END, "=" * 50 + "\n")

    # Disable the button while processing
    button.config(state=tk.DISABLED)

    def process():
        try:
            # Phase 0: System Detection (5%)
            update_progress(5, "Detecting system information...")
            log_debug("Detecting system information", "DEBUG")

            system_info = get_system_info()
            log_debug(f"System: {system_info['os']} {system_info['arch']}", "INFO")
            log_debug(f"Package Manager: {system_info['package_manager']}", "INFO")

            # Phase 1: Get requirements and check commands (10-30%)
            update_progress(10, "Analyzing requirements with AI...")
            log_debug("Phase 1: Sending requirements request to AI", "API")

            # Smart model selection: Use Kimi K2 for code-related tasks, Llama for basic tasks
            smart_model = select_smart_model(user_input, selected_model)
            log_debug(f"Smart model selection: {smart_model}", "INFO")

            payload = {
                "query": user_input,
                "system_info": system_info,
                "model": smart_model
            }

            log_debug(f"Sending POST to {SERVER_BASE_URL}/get-requirements", "NETWORK")
            log_debug(f"Payload: {json.dumps(payload, indent=2)}", "DEBUG")

            try:
                response = requests.post(f"{SERVER_BASE_URL}/get-requirements", json=payload, timeout=30)
                log_debug(f"Server response status: {response.status_code}", "NETWORK")

                if response.status_code != 200:
                    log_debug(f"Server error: {response.text}", "ERROR")
                    raise Exception(f"Server returned {response.status_code}: {response.text}")

                requirements_data = response.json()
                log_debug("Requirements response received successfully", "SUCCESS")

            except requests.exceptions.Timeout:
                log_debug("Request timed out after 30 seconds", "ERROR")
                raise Exception("AI request timed out")
            except requests.exceptions.ConnectionError:
                log_debug("Failed to connect to server", "ERROR")
                raise Exception("Cannot connect to server")
            except Exception as e:
                log_debug(f"Request failed: {str(e)}", "ERROR")
                raise

            update_progress(30, "AI analysis complete")

            log_debug(f"Package: {requirements_data.get('package_name', 'Unknown')}", "INFO")
            log_debug(f"Explanation: {requirements_data.get('explanation', '')}", "INFO")

            # Phase 2: Background checks (30-50%)
            check_commands = requirements_data.get("check_commands", [])
            check_results = []

            if check_commands:
                update_progress(35, "Running system checks...")
                log_debug(f"Phase 2: Running {len(check_commands)} background checks", "DEBUG")

                # Run background checks for automated processing
                for i, cmd in enumerate(check_commands):
                    progress = 35 + (15 * i / len(check_commands))
                    update_progress(int(progress), f"Checking: {cmd}")
                    log_debug(f"Running check: {cmd}", "DEBUG")

                    result = run_command_in_background(cmd)
                    check_results.append(result)

                    status_icon = "✓" if result["success"] else "✗"
                    log_debug(f"{status_icon} {cmd}: {result['output'][:50]}...", "DEBUG" if len(result['output']) > 50 else "DEBUG")

                update_progress(50, "System checks complete")

            # Phase 3: Generate installation commands (50-70%)
            update_progress(55, "Generating installation plan with AI...")
            log_debug("Phase 3: Sending install commands request to AI", "API")

            # Use smart model selection for install commands too
            smart_install_model = select_smart_model(user_input, selected_model)

            install_payload = {
                "query": user_input,
                "system_info": system_info,
                "check_results": check_results,
                "model": smart_install_model
            }

            log_debug(f"Sending POST to {SERVER_BASE_URL}/generate-install-commands", "NETWORK")
            log_debug(f"Check results summary: {len(check_results)} items", "DEBUG")

            try:
                install_response = requests.post(f"{SERVER_BASE_URL}/generate-install-commands", json=install_payload, timeout=30)
                log_debug(f"Install response status: {install_response.status_code}", "NETWORK")

                if install_response.status_code != 200:
                    log_debug(f"Install server error: {install_response.text}", "ERROR")
                    raise Exception(f"Server returned {install_response.status_code}: {install_response.text}")

                install_data = install_response.json()
                log_debug("Install commands response received successfully", "SUCCESS")

            except requests.exceptions.Timeout:
                log_debug("Install request timed out after 30 seconds", "ERROR")
                raise Exception("AI install request timed out")
            except Exception as e:
                log_debug(f"Install request failed: {str(e)}", "ERROR")
                raise

            update_progress(70, "Installation plan generated")
            log_debug(f"Plan: {install_data.get('explanation', '')}", "INFO")
            log_debug(f"Estimated time: {install_data.get('estimated_time', 'Unknown')}", "INFO")

            # Phase 4: Execute commands in terminal (70-100%)
            install_commands = install_data.get("commands", [])
            if install_commands or check_commands:
                update_progress(75, "Preparing terminal execution...")
                log_debug(f"Phase 4: Preparing {len(install_commands)} install commands", "DEBUG")

                # Prepare install commands with sudo where needed
                sudo_commands = []
                for cmd in install_commands:
                    if not cmd.startswith('sudo') and any(keyword in cmd.lower() for keyword in ['apt', 'yum', 'dnf', 'pacman', 'install']):
                        sudo_commands.append(f"sudo {cmd}")
                    else:
                        sudo_commands.append(cmd)

                package_name = requirements_data.get('package_name', user_input)

                update_progress(85, f"Opening terminal for {package_name}...")
                log_debug(f"Opening terminal for {package_name}", "INFO")

                # Choose terminal opening method based on mode
                if agent_mode:
                    log_debug("Using agent mode - automatic sudo execution", "INFO")
                    # Open terminal in agent mode with automatic sudo
                    success, message = open_terminal_agent_mode(
                        check_commands,
                        sudo_commands,
                        package_name,
                        sudo_password,
                        f"AI Installer Agent - {package_name}"
                    )
                else:
                    log_debug("Using manual mode - user handles sudo prompts", "INFO")
                    # Open single terminal for both checking and installing IMMEDIATELY
                    success, message = open_terminal_with_commands(
                        check_commands,
                        sudo_commands,
                        package_name,
                        f"AI Installer - {package_name}"
                    )

                # Brief pause to let terminal start
                time.sleep(0.5)

                # Then update UI with final status
                if success:
                    update_progress(100, "Terminal launched successfully!")
                    if agent_mode:
                        log_debug("Agent mode terminal launched - automatic execution", "SUCCESS")
                    else:
                        log_debug("Manual mode terminal launched - monitor for progress", "SUCCESS")
                else:
                    update_progress(100, "Terminal launch failed")
                    log_debug(f"Terminal launch failed: {message}", "ERROR")
            else:
                update_progress(100, "No commands to execute")
                log_debug("No installation commands generated", "INFO")

            log_debug("Installation process completed!", "SUCCESS")
            status_text.insert(tk.END, "\n" + "="*50 + "\n")

        except requests.exceptions.RequestException as e:
            update_progress(0, "Network error occurred")
            log_debug(f"Network Error: {str(e)}", "ERROR")
            log_debug("Make sure the server is running on localhost:8000", "ERROR")
        except Exception as e:
            update_progress(0, "Error occurred")
            log_debug(f"Unexpected Error: {str(e)}", "ERROR")
        finally:
            status_text.insert(tk.END, "=" * 50 + "\n\n")
            button.config(state=tk.NORMAL)
            # Auto-scroll to bottom
            status_text.see(tk.END)

            # Reset progress if not completed successfully
            if progress_var.get().find("100%") == -1:
                update_progress(0, "Ready for next request")

    threading.Thread(target=process).start()

# UI Setup
root = tk.Tk()
root.title("AI Package Installer")
root.geometry("900x700")

# Header
header_frame = tk.Frame(root)
header_frame.pack(pady=10, padx=10, fill=tk.X)

title_label = tk.Label(header_frame, text="AI Package Installer", font=("Arial", 16, "bold"))
title_label.pack()

subtitle_label = tk.Label(header_frame, text="Intelligent package installation with Groq AI Models", font=("Arial", 10))
subtitle_label.pack()

# Input section
input_frame = tk.Frame(root)
input_frame.pack(pady=10, padx=10, fill=tk.X)

tk.Label(input_frame, text="What do you want to install?", font=("Arial", 12)).pack(anchor=tk.W)

entry_frame = tk.Frame(input_frame)
entry_frame.pack(fill=tk.X, pady=5)

entry = tk.Entry(entry_frame, width=60, font=("Arial", 11))
entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

# Model selection frame
model_frame = tk.Frame(input_frame)
model_frame.pack(fill=tk.X, pady=5)

tk.Label(model_frame, text="AI Model:", font=("Arial", 10)).pack(side=tk.LEFT)

# Model selection variable and dropdown
model_var = tk.StringVar()
model_var.set("moonshotai/kimi-k2-instruct")  # Always use Kimi K2

# Function to fetch models from server
def fetch_available_models():
    """Fetch available models from the server."""
    try:
        response = requests.get(f"{SERVER_BASE_URL}/models", timeout=5)
        if response.status_code == 200:
            data = response.json()
            models = data.get('available_models', {})
            default_model = data.get('default_model', 'llama-3.3-70b-versatile')

            # Create options list with proper formatting
            options = []
            for model_id, info in models.items():
                recommended = " (Recommended)" if info.get('recommended') else ""
                display_name = f"{info['name']}{recommended}"
                options.append((display_name, model_id))

            return options, default_model
        else:
            log_debug(f"Failed to fetch models: {response.status_code}", "ERROR")
            return get_fallback_models(), "moonshotai/kimi-k2-instruct"
    except Exception as e:
        log_debug(f"Error fetching models: {e}", "ERROR")
        return get_fallback_models(), "moonshotai/kimi-k2-instruct"

def get_fallback_models():
    """Fallback models if server is not available."""
    return [
        ("Moonshot AI Kimi K2 Instruct (Default)", "moonshotai/kimi-k2-instruct"),
        ("Llama 3.3 70B Versatile", "llama-3.3-70b-versatile"),
        ("Llama 3.1 8B Instant (Fast)", "llama-3.1-8b-instant"),
        ("Gemma2 9B Instruct (Balanced)", "gemma2-9b-it"),
        ("DeepSeek R1 Distilled Llama 70B", "deepseek-r1-distill-llama-70b")
    ]

# Try to fetch models from server, use fallback if needed
model_options, default_model = fetch_available_models()
model_var.set(default_model)

model_dropdown = tk.OptionMenu(model_frame, model_var, *[option[1] for option in model_options])
model_dropdown.config(font=("Arial", 9), width=30)
model_dropdown.pack(side=tk.LEFT, padx=(5, 10))

# Refresh models button
def refresh_models():
    """Refresh the models list from server."""
    global model_options, model_dropdown
    log_debug("Refreshing models list from server...", "INFO")

    new_options, new_default = fetch_available_models()
    model_options = new_options

    # Recreate the dropdown with new options
    model_dropdown.destroy()
    model_dropdown = tk.OptionMenu(model_frame, model_var, *[option[1] for option in model_options])
    model_dropdown.config(font=("Arial", 9), width=30)
    model_dropdown.pack(side=tk.LEFT, padx=(5, 10))

    # Update the display names in the dropdown
    menu = model_dropdown['menu']
    menu.delete(0, 'end')
    for display_name, model_id in model_options:
        menu.add_command(label=display_name, command=tk._setit(model_var, model_id))

    log_debug(f"Models refreshed: {len(model_options)} available", "SUCCESS")

refresh_button = tk.Button(model_frame, text="🔄", command=refresh_models,
                          font=("Arial", 8), width=3, height=1)
refresh_button.pack(side=tk.LEFT, padx=(2, 0))

# Agent mode frame
agent_frame = tk.Frame(input_frame)
agent_frame.pack(fill=tk.X, pady=5)

# Agent mode checkbox
agent_mode_var = tk.BooleanVar()
agent_checkbox = tk.Checkbutton(agent_frame, text="Agent Mode (Auto-execute with sudo)",
                               variable=agent_mode_var, font=("Arial", 10))
agent_checkbox.pack(side=tk.LEFT)

# Password entry for agent mode
password_label = tk.Label(agent_frame, text="Sudo Password:", font=("Arial", 10))
password_label.pack(side=tk.LEFT, padx=(20, 5))

password_entry = tk.Entry(agent_frame, show="*", width=20, font=("Arial", 10))
password_entry.pack(side=tk.LEFT, padx=(0, 10))

# Buttons frame
button_frame = tk.Frame(input_frame)
button_frame.pack(fill=tk.X, pady=5)

button = tk.Button(button_frame, text="Install", command=install_package,
                  bg="#4CAF50", fg="white", font=("Arial", 11, "bold"), width=10)
button.pack(side=tk.RIGHT, padx=(10, 0))

# Clear button
clear_button = tk.Button(button_frame, text="Clear Log",
                        command=lambda: status_text.delete(1.0, tk.END),
                        bg="#f44336", fg="white", font=("Arial", 11), width=10)
clear_button.pack(side=tk.RIGHT, padx=(5, 0))

# Progress section
progress_frame = tk.Frame(root)
progress_frame.pack(pady=5, padx=10, fill=tk.X)

tk.Label(progress_frame, text="Progress:", font=("Arial", 10, "bold")).pack(anchor=tk.W)

progress_var = tk.StringVar()
progress_var.set("[" + "-" * 50 + "] 0% - Ready")

progress_label = tk.Label(progress_frame, textvariable=progress_var,
                         font=("Consolas", 10), bg="#f0f0f0", anchor=tk.W)
progress_label.pack(fill=tk.X, pady=2)

# Status section
status_frame = tk.Frame(root)
status_frame.pack(pady=10, padx=10, fill=tk.BOTH, expand=True)

tk.Label(status_frame, text="Installation Log:", font=("Arial", 12, "bold")).pack(anchor=tk.W)

# Text widget with scrollbar
text_frame = tk.Frame(status_frame)
text_frame.pack(fill=tk.BOTH, expand=True, pady=5)

status_text = tk.Text(text_frame, height=25, width=100, font=("Consolas", 10),
                     wrap=tk.WORD, bg="#f8f8f8")
scrollbar = tk.Scrollbar(text_frame, orient=tk.VERTICAL, command=status_text.yview)
status_text.configure(yscrollcommand=scrollbar.set)

status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

# Bind Enter key to install
entry.bind('<Return>', lambda event: install_package())

# Initial message
status_text.insert(tk.END, "Welcome to AI Package Installer!\n")
status_text.insert(tk.END, "Enter a package name or description (e.g., 'python 3.9', 'nodejs 16', 'install docker 20.10')\n")
status_text.insert(tk.END, "The system will intelligently determine requirements and installation steps.\n")
status_text.insert(tk.END, "\nModes:\n")
status_text.insert(tk.END, "• Manual Mode: Commands execute in terminal, you handle sudo prompts\n")
status_text.insert(tk.END, "• Agent Mode: Fully automated execution with your sudo password\n\n")

root.mainloop()
